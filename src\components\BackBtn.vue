<template>
  <button class="btn btn-outline-purple d-flex align-items-center gap-2" @click="handleClick">
    <i class="fas fa-arrow-left"></i>
    <span class="d-none d-sm-block">
      {{ text }}
    </span>
  </button>



</template>

<script setup>
// 定義 props
defineProps({
  text: {
    type: String,
    default: '返回'
  },
  to: {
    type: String,
    default: null
  }
})

// 定義 emits
const emit = defineEmits(['click'])

// 處理點擊事件
const handleClick = () => {
  emit('click')
}
</script>

<style scoped>
/* 如果需要額外的樣式可以在這裡添加 */
</style>
