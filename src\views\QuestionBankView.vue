<template>
  <div>
    <!-- 頂部導航欄 -->
    <TopNavigation />

    <div class="exam-system-container">
      <div class="container-fluid">
        <div class="row">
          <!-- 主要內容區域 -->
          <div class="col-12">
            <div class="exam-card h-100">
              <!-- 返回按鈕 -->
              <BackBtn text="返回章節選擇" @click="goBack" />

              <div v-if="!questions.length" class="text-center py-5">
                <h4 class="text-muted">找不到題庫資料</h4>
                <router-link to="/chapters" class="btn btn-gradient">返回章節選擇</router-link>
              </div>

              <div v-else>
                <!-- 題庫資訊卡片 -->
                <div class="card text-white my-4"
                  style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                  <div class="card-body">
                    <div class="row align-items-center">
                      <div class="col-md-8">
                        <h3 class="mb-2">
                          <i class="fas fa-book me-2"></i>
                          {{ currentChapter?.title }}
                        </h3>
                        <div class="d-flex gap-3 flex-wrap">
                          <span>
                            <i class="fas fa-language me-1"></i>
                            {{ currentLanguageText }}
                          </span>
                          <span>
                            <i class="fas fa-question-circle me-1"></i>
                            共 {{ questions.length }} 題
                          </span>
                        </div>
                      </div>
                      <div class="col-md-4 text-end">
                        <div class="display-4 fw-bold">📖</div>
                        <div class="fs-5 fw-bold">題庫模式</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 題目列表 -->
                <div class="d-grid gap-4">
                  <div v-for="(question, index) in questions" :key="index" class="question-bank-card">
                    <!-- 題目內容 -->
                    <div class="card border-0 bg-light">
                      <div class="card-body">
                        <!-- 題目標題 -->
                        <div class="d-flex align-items-center justify-content-between mb-3">
                          <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-success fs-6">
                              <i class="fas fa-book me-1"></i>
                              第 {{ index + 1 }} 題
                            </span>
                            <span v-if="question.type === 'multiple'" class="badge bg-info fs-6">
                              <i class="fas fa-check-double me-1"></i>
                              複選題
                            </span>
                            <span v-else class="badge bg-primary fs-6">
                              <i class="fas fa-check me-1"></i>
                              單選題
                            </span>
                          </div>
                        </div>

                        <h5 class="card-title mb-4">
                          {{ question.q }}
                        </h5>

                        <!-- 選項 -->
                        <div class="d-grid gap-2 mb-3">
                          <div v-for="(option, optionIndex) in question.options" :key="optionIndex" class="option-bank"
                            :class="{
                              'correct-answer': isCorrectAnswer(optionIndex, question.answer)
                            }">
                            <div class="option-letter">{{ String.fromCharCode(65 + optionIndex) }}</div>
                            <div class="flex-grow-1">{{ option }}</div>
                            <div v-if="isCorrectAnswer(optionIndex, question.answer)" class="option-indicator">
                              <i class="fas fa-check text-success"></i>
                            </div>
                          </div>
                        </div>

                        <!-- 正確答案區域 -->
                        <div class="correct-answer-section">
                          <div class="correct-answer">
                            <strong>正確答案：</strong>
                            <span v-if="Array.isArray(question.answer)">
                              {{question.answer.map(ans => String.fromCharCode(65 + ans)).join(', ')}}
                            </span>
                            <span v-else>
                              {{ String.fromCharCode(65 + question.answer) }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 操作按鈕區域 -->
                <div class="d-flex gap-3 justify-content-center mb-4 flex-wrap mt-4">
                  <button class="btn btn-gradient d-flex align-items-center gap-2" @click="startExam">
                    <i class="fas fa-play"></i>
                    測驗這章
                  </button>
                  <button class="btn btn-secondary d-flex align-items-center gap-2" @click="goToChapters">
                    <i class="fas fa-list"></i>
                    回題庫章節
                  </button>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import TopNavigation from '../components/TopNavigation.vue'
import BackBtn from '../components/BackBtn.vue'

const router = useRouter()
const examStore = useExamStore()

// 檢查登入狀態
if (!examStore.user.isLoggedIn) {
  router.push('/')
}

const questions = computed(() => examStore.questions)
const currentChapter = computed(() => examStore.currentChapter)

const currentLanguageText = computed(() => {
  return examStore.currentLanguage === '中文' ? '中文' : '英文'
})

const isCorrectAnswer = (optionIndex, correctAnswer) => {
  if (Array.isArray(correctAnswer)) {
    return correctAnswer.includes(optionIndex)
  }
  return optionIndex === correctAnswer
}

const goBack = () => {
  router.push('/chapters')
}

const startExam = () => {
  if (currentChapter.value) {
    examStore.setMode('exam')
    router.push(`/exam/${currentChapter.value.id}`)
  }
}

const goToChapters = () => {
  examStore.setMode('questionBank')
  router.push('/chapters')
}

onMounted(() => {
  if (!questions.value.length) {
    router.push('/chapters')
  }
})
</script>

<style scoped>
.question-bank-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.option-bank {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.option-bank.correct-answer {
  border-color: #28a745;
  background: #d4edda;
  color: #155724;
}

.option-letter {
  font-weight: bold;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e1e5e9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.option-bank.correct-answer .option-letter {
  background: #28a745;
  color: white;
}

.correct-answer-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.correct-answer {
  color: #495057;
  font-size: 0.95rem;
}

.option-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  color: white;
}

.btn-gradient:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  color: white;
}

@media (max-width: 768px) {
  .question-bank-card {
    padding: 20px 15px;
  }

  .option-bank {
    padding: 12px 15px;
  }

  .display-4 {
    font-size: 2.5rem !important;
  }

  .d-flex.gap-3 {
    flex-direction: column;
  }
}
</style>
