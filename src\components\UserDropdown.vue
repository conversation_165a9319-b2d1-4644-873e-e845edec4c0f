<template>
  <div class="user-dropdown">
    <div class="dropdown">
      <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-user"></i>
        {{ getUserDisplayName }}
      </button>
      <ul class="dropdown-menu">
        <li>
          <router-link to="/history" class="dropdown-item" @click="closeDropdown">
            <i class="fas fa-history"></i>
            考試紀錄
          </router-link>
        </li>
        <li>
          <hr class="dropdown-divider">
        </li>
        <li>
          <button class="dropdown-item text-danger" @click="handleLogout">
            <i class="fas fa-sign-out-alt"></i>
            登出
          </button>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'

const router = useRouter()
const examStore = useExamStore()

// 獲取用戶顯示名稱（電郵@前的部分）
const getUserDisplayName = computed(() => {
  if (examStore.user.email) {
    return examStore.user.email.split('@')[0]
  }
  return '用戶'
})

const handleLogout = async () => {
  await examStore.logout()
  router.push('/')
  closeDropdown()
}

const closeDropdown = () => {
  // 關閉 Bootstrap dropdown
  const dropdownElement = document.querySelector('.dropdown-toggle')
  if (dropdownElement) {
    const dropdown = bootstrap.Dropdown.getInstance(dropdownElement)
    if (dropdown) {
      dropdown.hide()
    }
  }
}
</script>

<style scoped>
/* 樣式已在 main.css 中定義 */
</style>
