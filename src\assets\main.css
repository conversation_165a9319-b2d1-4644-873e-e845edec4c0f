/* 移除不存在的 base.css 引用 */

/* 全域重置和基礎樣式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

#app {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 50px 20px;
  background-image: linear-gradient(120deg, #e0c3fc 0%, #8ec5fc 100%);
  display: flex;
  flex-direction: column;
}

.exam-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  padding: 40px;
  transition: all 0.3s ease;
  position: relative;
  animation: fadeIn 0.5s ease-in;
}

.exam-card.large {
  max-width: 900px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 漸層文字效果 */
.gradient-text {
  color: #647ce8;
}

/* 自訂按鈕樣式 */
.btn-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  color: white;
}

.btn-gradient:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 返回按鈕 */
.btn {
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #667eea;
  color: white;
  transform: scale(1.05);
  text-decoration: none;
}

.btn-outline-purple {
  color: #667eea;
  background: #f0f4ff;
  border: 2px solid #667eea;
}

.btn-outline-purple:hover {
  background: #667eea;
  color: white;
}

/* 語言卡片 */
.language-card {
  padding: 20px;
  border-radius: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  text-decoration: none;
  color: inherit;
}

.language-card.chinese {
  background-image: linear-gradient(to top, #bdc2e8 0%, #bdc2e8 1%, #e6dee9 100%);
  color: #333;
}

.language-card.english {
  background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
  color: #333;
}

.language-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  text-decoration: none;
  color: inherit;
}

/* 章節卡片 */
.chapter-card {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 15px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
}

.chapter-card:hover::before {
  transform: scaleX(1);
}

.chapter-card:hover {
  border-color: #667eea;
  background: #667eea17;
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
  text-decoration: none;
}

/* 選項樣式 */
.option-item {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.option-item:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.option-item.selected {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.option-item.correct {
  border-color: #28a745;
  background: #28a745;
  color: white;
}

.option-item.incorrect {
  border-color: #dc3545;
  background: #dc3545;
  color: white;
}

.option-letter {
  font-weight: bold;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e1e5e9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.option-item.selected .option-letter,
.option-item.correct .option-letter,
.option-item.incorrect .option-letter {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 進度條 */
.progress-custom {
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-custom .progress-bar {
  background: linear-gradient(45deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

/* 用戶下拉選單 */
.user-dropdown {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 1000;
}

.user-dropdown .dropdown-toggle {
  background: #f0f4ff;
  border: 2px solid #667eea;
  color: #667eea;
  font-weight: bold;
  border-radius: 8px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-dropdown .dropdown-toggle:hover,
.user-dropdown .dropdown-toggle:focus {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.user-dropdown .dropdown-toggle::after {
  margin-left: 5px;
}

.user-dropdown .dropdown-menu {
  border: 2px solid #667eea;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
}

.user-dropdown .dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  transition: all 0.3s ease;
}

.user-dropdown .dropdown-item:hover {
  background: #f0f4ff;
  color: #667eea;
}

.user-dropdown .dropdown-item.text-danger:hover {
  background: #f8d7da;
  color: #dc3545;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .exam-card {
    padding: 30px 20px;
    margin: 10px;
  }

  .language-card {
    padding: 30px 15px;
  }

  .chapter-card {
    padding: 20px;
  }

  .user-dropdown {
    position: fixed;
    top: 10px;
    right: 10px;
  }

  .user-dropdown .dropdown-toggle {
    padding: 6px 12px;
    font-size: 0.9rem;
  }
}

/* 答題回顧樣式 - 參考原始設計 */
.question-review-card {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 25px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.review-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.review-question {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 25px;
  border: 1px solid #e9ecef;
}

.review-header {
  color: #dc3545;
  font-weight: bold;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.review-question-text,
.question-text {
  font-size: 1.2rem;
  margin-bottom: 25px;
  color: #333;
  line-height: 1.6;
  font-weight: 500;
}

.review-options,
.options-review {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.review-option,
.option-review {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 15px;
  border-radius: 8px;
  border: 2px solid #e1e5e9;
  background: white;
  transition: all 0.3s ease;
}

.review-option.correct,
.option-review.correct {
  border-color: #28a745;
  background: #28a745;
  color: white;
}

.review-option.incorrect,
.option-review.incorrect {
  border-color: #dc3545;
  background: #dc3545;
  color: white;
}

.review-option .option-letter,
.option-review .option-letter {
  font-weight: bold;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: #e1e5e9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.review-option.correct .option-letter,
.option-review.correct .option-letter {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.review-option.incorrect .option-letter,
.option-review.incorrect .option-letter {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.review-option .option-text,
.option-review .option-text {
  flex: 1;
}

.review-option .option-indicator,
.option-review .option-indicator {
  font-size: 1.2rem;
  font-weight: bold;
}

@media (max-width: 768px) {
  #app {
    padding: 40px 10px;
  }
}

.rem3 {
  font-size: 3rem;
}
