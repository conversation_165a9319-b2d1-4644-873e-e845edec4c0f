<script setup>
import { RouterView } from 'vue-router'
import { useExamStore } from './stores/examStore'

const examStore = useExamStore()
</script>

<template>
  <div id="app">
    <RouterView :key="$route.path + examStore.currentLanguage" />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  width: 100%;
  min-height: 100vh;
}

body {
  margin: 0;
  padding: 0;
}
</style>
