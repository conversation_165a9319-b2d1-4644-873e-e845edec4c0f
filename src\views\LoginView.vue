<template>
  <div class="col-6 mx-auto">
    <div class="exam-card">
      <div class="text-center mb-4">
        <h1 class="display-4 gradient-text fw-bold mb-3">📚 考試練習系統</h1>
        <p class="text-muted fs-5">提升您的學習成效</p>
      </div>

      <form @submit.prevent="handleLogin">
        <div class="mb-3">
          <label class="form-label fw-semibold">電子郵件</label>
          <input type="email" class="form-control form-control-lg" v-model="email" placeholder="請輸入電子郵件" required>
        </div>

        <div class="mb-4">
          <label class="form-label fw-semibold">密碼</label>
          <input type="password" class="form-control form-control-lg" v-model="password" placeholder="請輸入密碼" required>
        </div>

        <button type="submit"
          class="btn btn-gradient btn-lg w-100 d-flex align-items-center justify-content-center gap-2"
          :disabled="!email || !password || isLoading">
          <i class="fas fa-sign-in-alt" v-if="!isLoading"></i>
          <div class="spinner-border spinner-border-sm" role="status" v-if="isLoading">
            <span class="visually-hidden">Loading...</span>
          </div>
          {{ isLoading ? '登入中...' : '登入系統' }}
        </button>
      </form>

      <div v-if="errorMessage" class="alert alert-danger mt-3 d-flex align-items-center gap-2">
        <i class="fas fa-exclamation-triangle"></i>
        {{ errorMessage }}
      </div>

      <div class="text-center mt-4">
        <p class="text-muted">
          還沒有帳號？
          <router-link to="/register" class="text-decoration-none fw-semibold">
            立即註冊
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import { loginUser } from '../firebase/auth'

const router = useRouter()
const examStore = useExamStore()

const email = ref('')
const password = ref('')
const errorMessage = ref('')
const isLoading = ref(false)

const handleLogin = async () => {
  errorMessage.value = ''
  isLoading.value = true

  try {
    const result = await loginUser(email.value, password.value)

    if (result.success) {
      // 登入成功，更新 store 狀態
      examStore.setUser(result.user)
      router.push('/language')
    } else {
      // 處理 Firebase 錯誤訊息
      if (result.error.includes('user-not-found')) {
        errorMessage.value = '找不到此電子郵件的帳號'
      } else if (result.error.includes('wrong-password')) {
        errorMessage.value = '密碼錯誤'
      } else if (result.error.includes('invalid-email')) {
        errorMessage.value = '電子郵件格式不正確'
      } else if (result.error.includes('too-many-requests')) {
        errorMessage.value = '登入嘗試次數過多，請稍後再試'
      } else {
        errorMessage.value = '登入失敗：' + result.error
      }
    }
  } catch (error) {
    errorMessage.value = error;
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
@media (max-width: 480px) {
  .exam-card {
    padding: 30px 20px;
  }

  .display-4 {
    font-size: 2rem !important;
  }
}
</style>
