<template>
  <div>
    <!-- 頂部導航欄 -->
    <TopNavigation />

    <div class="exam-system-container">
      <div class="container-fluid">
        <div class="row">
          <!-- 答題卡側邊欄 -->
          <div class="col-lg-3">
            <AnswerCard v-if="examDetail" :questions="examDetail.questions" :user-answers="examDetail.userAnswers"
              :current-index="currentQuestionIndex" :correct-answers="examDetail.questions.map(q => q.answer)"
              mode="review" @jump-to-question="jumpToQuestion" />
          </div>

          <!-- 主要內容區域 -->
          <div class="col-lg-9">
            <div class="exam-card h-100">
              <div v-if="!examDetail" class="text-center py-5">
                <h4 class="text-muted">找不到考試記錄</h4>
                <router-link to="/history" class="btn btn-gradient">返回歷史記錄</router-link>
              </div>

              <div v-else>
                <!-- 考試資訊卡片 -->
                <div class="card text-white mb-4"
                  style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                  <div class="card-body">
                    <div class="row align-items-center ">
                      <div class="col-md-8">
                        <h3 class="mb-2">
                          <i class="fas fa-clipboard-check me-2"></i>
                          {{ examDetail.chapter.title }}
                        </h3>
                        <div class="d-flex gap-3 flex-wrap">
                          <span>
                            <i class="fas fa-language me-1"></i>
                            {{ examDetail.languageText }}
                          </span>
                          <span>
                            <i class="fas fa-calendar me-1"></i>
                            {{ examDetail.date }}
                          </span>
                        </div>
                      </div>
                      <div class="col-md-4 text-end d-flex align-items-center justify-content-end gap-4">
                        <div>
                          <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            ✔️ {{ examDetail.correctCount }}/{{ examDetail.totalQuestions }} 題
                          </span>
                        </div>
                        <span class="display-3 fw-bold">{{ examDetail.score }}分</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 答題回顧標題 -->
                <div class="text-center mb-4">
                  <h3 class="d-flex align-items-center justify-content-center gap-2">
                    <i class="fas fa-search"></i>
                    答題回顧
                  </h3>
                </div>

                <!-- 全部答對的情況 -->
                <div v-if="examDetail.incorrectQuestions.length === 0" class="alert alert-success text-center p-4">
                  <i class="fas fa-trophy fs-1 mb-3 d-block"></i>
                  <h4 class="fw-bold mb-2">🎊 恭喜！全部答對！</h4>
                  <p class="mb-0">您的表現非常出色！</p>
                </div>

                <!-- 題目回顧 -->
                <div v-else class="d-grid gap-4">
                  <div v-for="(question, index) in examDetail.questions" :key="index" class="question-review-card">
                    <!-- 題目內容 -->
                    <div class="card border-0 bg-light">
                      <div class="card-body">
                        <!-- 答題狀態 -->
                        <div class="d-flex align-items-center justify-content-between mb-3">
                          <div class="d-flex align-items-center gap-2">
                            <span v-if="isAnswerCorrect(examDetail.userAnswers[index], question.answer)"
                              class="badge bg-success fs-6">
                              <i class="fas fa-check me-1"></i>
                              答對
                            </span>
                            <span v-else class="badge bg-danger fs-6">
                              <i class="fas fa-times me-1"></i>
                              答錯
                            </span>
                          </div>
                        </div>

                        <h5 class="card-title mb-4">
                          {{ index + 1 }}. {{ question.q }}
                        </h5>

                        <!-- 選項 -->
                        <div class="d-grid gap-2">
                          <div v-for="(option, optionIndex) in question.options" :key="optionIndex"
                            class="option-review" :class="{
                              'user-answer': examDetail.userAnswers[index] === optionIndex || (Array.isArray(examDetail.userAnswers[index]) && examDetail.userAnswers[index].includes(optionIndex))
                            }">
                            <div class="option-letter">{{ String.fromCharCode(65 + optionIndex) }}</div>
                            <div class="flex-grow-1">{{ option }}</div>
                          </div>
                        </div>

                        <!-- 正確答案區域 -->
                        <div class="correct-answer-section mt-3 pt-3 border-top">
                          <div class="correct-answer">
                            <strong>正確答案：</strong>
                            <span v-if="Array.isArray(question.answer)">
                              {{question.answer.map(ans => String.fromCharCode(65 + ans)).join(', ')}}
                            </span>
                            <span v-else>
                              {{ String.fromCharCode(65 + question.answer) }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 操作按鈕 -->
                <div class="text-center mt-4">
                  <BackBtn text="返回歷史記錄" @click="goBack" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import TopNavigation from '../components/TopNavigation.vue'
import AnswerCard from '../components/AnswerCard.vue'
import BackBtn from '../components/BackBtn.vue'

const router = useRouter()
const route = useRoute()
const examStore = useExamStore()

// 檢查登入狀態
if (!examStore.user.isLoggedIn) {
  router.push('/')
}

const currentQuestionIndex = ref(0)

const examDetail = computed(() => {
  const examId = route.params.id
  console.log('尋找考試記錄 ID:', examId, '類型:', typeof examId)
  console.log('可用的考試記錄:', examStore.examHistory.map(exam => ({ id: exam.id, type: typeof exam.id })))

  // 嘗試字符串匹配和數字匹配
  return examStore.examHistory.find(exam =>
    exam.id === examId ||
    exam.id === parseInt(examId) ||
    exam.id.toString() === examId
  )
})

const jumpToQuestion = (index) => {
  currentQuestionIndex.value = index
  // 滾動到對應題目
  const element = document.querySelector(`.question-review-card:nth-child(${index + 1})`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const isAnswerCorrect = (userAnswer, correctAnswer) => {
  // 處理複選題
  if (Array.isArray(correctAnswer)) {
    if (!Array.isArray(userAnswer)) return false
    if (userAnswer.length !== correctAnswer.length) return false
    return userAnswer.every(ans => correctAnswer.includes(ans)) &&
      correctAnswer.every(ans => userAnswer.includes(ans))
  }
  // 處理單選題
  return userAnswer === correctAnswer
}

const goBack = () => {
  router.push('/history')
}

onMounted(() => {
  if (!examDetail.value) {
    router.push('/history')
  }
})
</script>

<style scoped>
.question-review-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.option-review {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.option-review.user-answer {
  border-color: #17a2b8;
  background: #d1ecf1;
  color: #0c5460;
}

.option-letter {
  font-weight: bold;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e1e5e9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.option-review.user-answer .option-letter {
  background: #17a2b8;
  color: white;
}

.correct-answer-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.correct-answer {
  color: #495057;
  font-size: 0.95rem;
}

.option-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .question-review-card {
    padding: 20px 15px;
  }

  .option-review {
    padding: 12px 15px;
  }

  .display-3 {
    font-size: 2.5rem !important;
  }
}
</style>
